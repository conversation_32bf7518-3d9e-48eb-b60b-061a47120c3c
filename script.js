// Initialize AOS Animation with mobile-friendly settings
document.addEventListener('DOMContentLoaded', function() {
    AOS.init({
        duration: 800,
        once: true,
        disable: 'mobile',
        startEvent: 'DOMContentLoaded'
    });

    // Theme Toggle Functionality
    const themeToggle = document.getElementById('theme-toggle');
    const themeIcon = themeToggle.querySelector('i');

    // Check for saved theme preference or use dark as default
    const currentTheme = localStorage.getItem('theme') || 'dark';
    document.documentElement.setAttribute('data-theme', currentTheme);

    // Update icon based on current theme
    if (currentTheme === 'dark') {
        themeIcon.classList.remove('fa-moon');
        themeIcon.classList.add('fa-sun');
    }

    // Toggle theme on button click
    themeToggle.addEventListener('click', () => {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';

        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);

        // Toggle icon
        themeIcon.classList.toggle('fa-moon');
        themeIcon.classList.toggle('fa-sun');
    });

    // Portfolio Filtering
    const filterButtons = document.querySelectorAll('.filter-btn');
    const portfolioItems = document.querySelectorAll('.portfolio-item');

    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked button
            button.classList.add('active');

            const filterValue = button.getAttribute('data-filter');

            // Filter portfolio items
            portfolioItems.forEach(item => {
                if (filterValue === 'all' || item.getAttribute('data-category') === filterValue) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            // Ensure navbar brand stays primary color when clicked
            if (this.classList.contains('navbar-brand')) {
                this.style.color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
            }

            // Set active class on clicked nav link (if it's a nav-link)
            if (this.classList.contains('nav-link')) {
                // Remove active class from all nav links
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });
                // Add active class to clicked link
                this.classList.add('active');

                // Store the clicked link's href to prevent scroll handler from overriding it immediately
                sessionStorage.setItem('lastClickedNav', targetId);

                // Set a timeout to allow the manual navigation to take precedence
                setTimeout(() => {
                    sessionStorage.removeItem('lastClickedNav');
                }, 1000); // Remove after 1 second to allow scroll-based activation to work again
            }

            if (targetElement) {
                const navbarHeight = document.querySelector('.navbar').offsetHeight;
                const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - navbarHeight;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });

                // Close mobile menu if open
                const navbarCollapse = document.querySelector('.navbar-collapse');
                if (navbarCollapse.classList.contains('show')) {
                    navbarCollapse.classList.remove('show');
                }
            }
        });
    });

    // Active navigation link based on scroll position
    window.addEventListener('scroll', () => {
        // Check if we have a recently clicked nav link
        const lastClickedNav = sessionStorage.getItem('lastClickedNav');

        // If we have a recently clicked nav link, don't override it with scroll-based activation
        if (lastClickedNav) {
            return;
        }

        const sections = document.querySelectorAll('section');
        const navLinks = document.querySelectorAll('.nav-link');

        let currentSection = '';

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            // We don't need sectionHeight for our calculation
            const navbarHeight = document.querySelector('.navbar').offsetHeight;

            if (window.pageYOffset >= sectionTop - navbarHeight - 50) {
                currentSection = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${currentSection}`) {
                link.classList.add('active');
            }
        });
    });

    // Form submission (prevent default for demo)
    const contactForm = document.querySelector('.contact-form');

    if (contactForm) {
        contactForm.addEventListener('submit', (e) => {
            e.preventDefault();
            alert('Thank you for your message! This is a demo form, so no message was actually sent.');
            contactForm.reset();
        });
    }

    // Mobile responsiveness fix
    function fixMobileResponsiveness() {
        // Force browser to recognize viewport changes
        const viewportMeta = document.querySelector('meta[name="viewport"]');
        if (viewportMeta) {
            viewportMeta.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
        }

        // Apply responsive styles immediately
        const isMobile = window.innerWidth < 768;
        document.body.classList.toggle('mobile-view', isMobile);

        // Ensure theme is applied correctly
        const currentTheme = localStorage.getItem('theme') || 'dark';
        document.documentElement.setAttribute('data-theme', currentTheme);

        // Fix horizontal scrolling issues
        document.documentElement.style.width = '100%';
        document.documentElement.style.overflowX = 'hidden';
        document.body.style.width = '100%';
        document.body.style.overflowX = 'hidden';

        // Fix portfolio images
        const portfolioImages = document.querySelectorAll('.portfolio-img');
        portfolioImages.forEach(img => {
            img.style.maxWidth = '100%';
            img.style.height = 'auto';
        });
    }

    // Ensure navbar brand has correct color
    function fixNavbarBrandColor() {
        const navbarBrand = document.querySelector('.navbar-brand');
        if (navbarBrand) {
            const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim();
            navbarBrand.style.color = primaryColor;
        }
    }

    // Function to set active nav link based on current URL hash
    function setActiveNavFromHash() {
        const hash = window.location.hash || '#home';
        const navLinks = document.querySelectorAll('.nav-link');

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === hash) {
                link.classList.add('active');
            }
        });
    }

    // Back to Top Button Functionality
    const backToTopButton = document.getElementById('back-to-top');

    // Show/hide back to top button based on scroll position
    function toggleBackToTopButton() {
        if (window.pageYOffset > 300) {
            backToTopButton.classList.add('visible');
        } else {
            backToTopButton.classList.remove('visible');
        }
    }

    // Add scroll event listener for back to top button
    window.addEventListener('scroll', toggleBackToTopButton);

    // Run on page load and resize
    window.addEventListener('load', () => {
        fixMobileResponsiveness();
        fixNavbarBrandColor();
        setActiveNavFromHash();
        toggleBackToTopButton();
    });
    window.addEventListener('resize', fixMobileResponsiveness);
    window.addEventListener('orientationchange', fixMobileResponsiveness);
    window.addEventListener('hashchange', setActiveNavFromHash);

    // Run immediately
    fixMobileResponsiveness();
    fixNavbarBrandColor();
    setActiveNavFromHash();
    toggleBackToTopButton();
});
