:root {
    /* Light Theme Variables */
    --primary-color: #2563eb; /* Modern blue */
    --primary-color-rgb: 37, 99, 235;
    --secondary-color: #64748b; /* Slate gray */
    --secondary-color-rgb: 100, 116, 139;
    --accent-color: #06b6d4; /* Cyan accent */
    --accent-color-rgb: 6, 182, 212;
    --success-color: #10b981; /* Emerald */
    --warning-color: #f59e0b; /* Amber */
    --background-color: #fefefe;
    --text-color: #1e293b;
    --text-muted: #64748b;
    --card-bg: rgba(255, 255, 255, 0.8);
    --glass-bg: rgba(255, 255, 255, 0.1);
    --border-color: rgba(226, 232, 240, 0.8);
    --shadow-color: rgba(0, 0, 0, 0.1);
    --shadow-lg: rgba(0, 0, 0, 0.15);
    --footer-bg: #f8fafc;
    --transition-speed: 0.3s;
    --transition-smooth: cubic-bezier(0.4, 0, 0.2, 1);
    --border-radius: 12px;
    --border-radius-lg: 20px;
}

/* Dark Theme Variables */
/* [data-theme="dark"] {

    --primary-color: #7bff00;
    --secondary-color: #adb5bd;
    --accent-color: #00c9ff;
    --background-color: #121212;
    --text-color: #f8f9fa;
    --card-bg: #1e1e1e;
    --border-color: #343a40;
    --shadow-color: rgba(255, 255, 255, 0.1);
    --footer-bg: #1e1e1e;
}  */

[data-theme="dark"] {
    --primary-color: #60a5fa; /* Bright blue for dark mode */
    --primary-color-rgb: 96, 165, 250;
    --secondary-color: #64748b; /* Muted slate */
    --secondary-color-rgb: 100, 116, 139;
    --accent-color: #06b6d4; /* Bright cyan */
    --accent-color-rgb: 6, 182, 212;
    --success-color: #10b981; /* Bright emerald */
    --warning-color: #f59e0b; /* Bright amber */
    --background-color: #020617; /* Almost black */
    --text-color: #f8fafc;
    --text-muted: #64748b;
    --card-bg: rgba(15, 23, 42, 0.9); /* Very dark with high opacity */
    --glass-bg: rgba(15, 23, 42, 0.4); /* Darker glass effect */
    --border-color: rgba(30, 41, 59, 0.6); /* Darker borders */
    --shadow-color: rgba(0, 0, 0, 0.6); /* Deeper shadows */
    --shadow-lg: rgba(0, 0, 0, 0.8); /* Much deeper shadows */
    --footer-bg: #0f172a; /* Very dark footer */
}


/* :root {

    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --accent-color: #00c9ff;
    --background-color: #ffffff;
    --text-color: #212529;
    --card-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --footer-bg: #f8f9fa;
    --transition-speed: 0.3s;
} */


* {
    box-sizing: border-box;
}

body {
    font-family: 'Helvetica', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    transition: background-color var(--transition-speed) var(--transition-smooth),
                color var(--transition-speed) var(--transition-smooth);
    line-height: 1.6;
    letter-spacing: -0.01em;
    font-weight: 400;
    font-size: 16px;
    overflow-x: hidden;
    scroll-behavior: smooth;
}

h1, h2, h3, h4, h5, h6 {
    /* font-family: 'Playfair Display', serif; */
    font-family: 'Helvetica', sans-serif;


    letter-spacing: -0.02em;
    font-weight: 600;
    margin-bottom: 1.2rem;
    line-height: 1.2;
}

.navbar-brand {
    /* font-family: 'JetBrains Mono', monospace; */
    font-family: 'Helvetica', sans-serif;

    font-weight: 600;
    letter-spacing: 0.05em;
}

p {
    margin-bottom: 1.5rem;
    font-size: 1.05rem;
    line-height: 1.7;
}

/* Modern glassmorphism utility classes */
.glass {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
}

.glass-card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

/* Navbar Styles */
.navbar {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 4px 20px var(--shadow-color);
    transition: all var(--transition-speed) var(--transition-smooth);
    padding: 1rem 0;
}

.navbar.scrolled {
    background: var(--card-bg);
    box-shadow: 0 8px 32px var(--shadow-lg);
}

/* Fix for navbar toggler in dark mode */
.navbar-toggler {
    border-color: var(--border-color);
    color: var(--text-color);
    padding: 0.25rem 0.5rem;
}

.navbar-toggler-icon {
    background-image: none !important;
    display: flex;
    align-items: center;
    justify-content: center;
}

.navbar-toggler-icon::before {
    content: '\f0c9';
    /* font-family: 'Font Awesome 5 Free'; */
    font-family: 'Helvetica', sans-serif;

    font-weight: 900;
    color: var(--text-color);
}

/* Mobile navbar styles */
@media (max-width: 991px) {
    .navbar-collapse {
        background-color: var(--card-bg);
        padding: 1rem;
        border-radius: 0.5rem;
        margin-top: 0.5rem;
        box-shadow: 0 5px 15px var(--shadow-color);
    }

    .nav-item {
        margin: 0.5rem 0;
    }

    .theme-toggle {
        margin-top: 0.5rem;
        padding: 0.5rem;
        width: 100%;
        text-align: left;
    }
}

.navbar-brand {
    color: var(--primary-color) !important;
    font-weight: 700;
    transition: color var(--transition-speed);
}

.navbar-brand:hover,
.navbar-brand:focus,
.navbar-brand:active,
.navbar-brand:visited {
    color: var(--primary-color) !important;
    text-decoration: none;
}

.nav-link {
    color: var(--text-color);
    margin: 0 10px;
    position: relative;
    transition: color var(--transition-speed);
}

.nav-link:hover,
.nav-link:focus,
.nav-link:active,
.nav-link.active {
    color: var(--primary-color) !important;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--primary-color);
    transition: width var(--transition-speed);
}

.nav-link:hover::after,
.nav-link:focus::after,
.nav-link:active::after,
.nav-link.active::after {
    width: 100%;
}

/* Fix for Bootstrap's default active/focus states */
.navbar-nav .nav-link.active,
.navbar-nav .nav-link:active,
.navbar-nav .show > .nav-link {
    color: var(--primary-color) !important;
}

/* Ensure navbar brand stays primary color in all states */
.navbar .navbar-brand,
.navbar .navbar-brand:hover,
.navbar .navbar-brand:focus,
.navbar .navbar-brand:active,
.navbar .navbar-brand:visited {
    color: var(--primary-color) !important;
}

/* Theme Toggle Button */
.theme-toggle {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    cursor: pointer;
    transition: color var(--transition-speed);
}

.theme-toggle:hover {
    color: var(--primary-color);
}

/* Hero Section */
#home {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg,
        var(--background-color) 0%,
        rgba(var(--primary-color-rgb), 0.05) 50%,
        var(--background-color) 100%);
    position: relative;
    overflow: hidden;
    padding-top: 100px;
}

/* Particles.js container */
#particles-js {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    right: 2rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
}

.scroll-line {
    width: 2px;
    height: 100px;
    background: linear-gradient(to bottom,
        transparent 0%,
        var(--primary-color) 50%,
        transparent 100%);
    position: relative;
    animation: scrollPulse 2s ease-in-out infinite;
}

.scroll-line::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 8px solid var(--primary-color);
}

@keyframes scrollPulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.hero-content {
    position: relative;
    z-index: 2;
}

/* Hero Badge */
.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 50px;
    padding: 0.75rem 1.5rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px var(--shadow-color);
    z-index: 3;
}

.badge-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--primary-color);
    font-family: 'Helvetica', sans-serif;
    white-space: nowrap;
}

.badge-pulse {
    width: 8px;
    height: 8px;
    background: var(--success-color);
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
    flex-shrink: 0;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
}

.hero-title {
    font-size: clamp(2.5rem, 8vw, 4.5rem);
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg,
        var(--primary-color) 0%,
        var(--accent-color) 50%,
        var(--primary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    position: relative;
    display: inline-block;
    letter-spacing: -0.02em;
    line-height: 1.1;
    animation: titleGlow 3s ease-in-out infinite alternate;
    animation: glitch 5s infinite;

}

@keyframes titleGlow {
    0% {
        filter: brightness(1);
        text-shadow: 0 0 20px rgba(var(--primary-color-rgb), 0.3);
    }
    100% {
        filter: brightness(1.1);
        text-shadow: 0 0 30px rgba(var(--accent-color-rgb), 0.4);
    }
}

/* Video editing glitch effect */
@keyframes glitch {
    0%, 90%, 100% {
        transform: translate(0);
        filter: none;
    }
    92% {
        transform: translate(-2px, 1px);
        text-shadow: -2px 0 2px rgba(var(--primary-color-rgb), 0.8);
        filter: hue-rotate(5deg);
    }
    93% {
        transform: translate(2px, -1px);
        text-shadow: 2px 0 2px rgba(var(--accent-color-rgb), 0.8);
        filter: hue-rotate(-5deg);
    }
    94% {
        transform: translate(-1px, -1px);
        text-shadow: 2px 0 2px rgba(var(--primary-color-rgb), 0.8);
        filter: brightness(1.1);
    }
    95% {
        transform: translate(1px, 1px);
        text-shadow: -2px 0 2px rgba(var(--accent-color-rgb), 0.8);
        filter: contrast(1.1);
    }
    96% {
        transform: translate(0);
        filter: none;
    }
}

.hero-subtitle {
    font-size: clamp(1.1rem, 3vw, 1.5rem);
    margin-bottom: 1rem;
    font-weight: 500;
    color: var(--primary-color);
    /* font-family: 'JetBrains Mono', monospace; */
    font-family: 'Helvetica', sans-serif;

    letter-spacing: 0.02em;
    opacity: 0.9;
}

.hero-description {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    margin-bottom: 3rem;
    font-weight: 400;
    color: var(--text-muted);
    line-height: 1.6;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Hero Buttons */
.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 3rem;
}

/* Hero Video Card */
.hero-video-card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
    box-shadow: 0 20px 60px var(--shadow-lg);
    overflow: hidden;
    max-width: 400px;
    width: 100%;
    transition: all var(--transition-speed) var(--transition-smooth);
    position: relative;
}

.hero-video-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 30px 80px var(--shadow-lg);
    border-color: var(--accent-color);
}

.video-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 1.5rem 1rem;
    background: linear-gradient(135deg,
        rgba(var(--primary-color-rgb), 0.1) 0%,
        rgba(var(--accent-color-rgb), 0.05) 100%);
}

.video-card-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    font-size: 0.875rem;
    font-weight: 600;
    font-family: 'Helvetica', sans-serif;
}

.video-card-badge i {
    font-size: 1rem;
}

.video-card-year {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    font-family: 'Helvetica', sans-serif;
}

.video-card-thumbnail {
    position: relative;
    aspect-ratio: 16/9;
    overflow: hidden;
}

.video-card-link {
    display: block;
    position: relative;
    width: 100%;
    height: 100%;
    text-decoration: none;
}

.video-card-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-speed) var(--transition-smooth);
}

.video-card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.3) 0%,
        rgba(0, 0, 0, 0.1) 50%,
        rgba(0, 0, 0, 0.3) 100%
    );
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-speed) var(--transition-smooth);
}

.video-card-play {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    transition: all var(--transition-speed) var(--transition-smooth);
    box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.4);
    margin-left: 3px; /* Optical alignment for play icon */
}

.video-card-duration {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
    font-family: 'Helvetica', sans-serif;
}

.video-card-footer {
    padding: 1.5rem;
}

.video-card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    /* font-family: 'Playfair Display', serif; */
    font-family: 'Helvetica', sans-serif;


}

.video-card-description {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin: 0;
    line-height: 1.5;
}

/* Hover Effects */
.hero-video-card:hover .video-card-img {
    transform: scale(1.05);
}

.hero-video-card:hover .video-card-play {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(var(--primary-color-rgb), 0.6);
}

.hero-video-card:hover .video-card-overlay {
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.2) 0%,
        rgba(0, 0, 0, 0.05) 50%,
        rgba(0, 0, 0, 0.2) 100%
    );
}

.cta-btn {
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 1rem;
    letter-spacing: 0.02em;
    transition: all var(--transition-speed) var(--transition-smooth);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
    min-width: 160px;
    justify-content: center;
    font-family: 'Helvetica', sans-serif;
}

.cta-btn.primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.3);
}

.cta-btn.secondary {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    color: var(--primary-color);
    border: 1px solid var(--border-color);
    box-shadow: 0 8px 25px var(--shadow-color);
}

.cta-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    transition: left 0.6s ease;
}

.cta-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(var(--primary-color-rgb), 0.4);
}

.cta-btn.primary:hover {
    color: white;
    box-shadow: 0 12px 35px rgba(var(--primary-color-rgb), 0.5);
}

.cta-btn.secondary:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.cta-btn:hover::before {
    left: 100%;
}

/* Enhanced Dark Theme Styles */
[data-theme="dark"] {
    /* Enhanced shadows for dark theme */
    --shadow-color: rgba(0, 0, 0, 0.7);
    --shadow-lg: rgba(0, 0, 0, 0.9);
}

[data-theme="dark"] body {
    background-color: #020617;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(96, 165, 250, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
}

[data-theme="dark"] .navbar {
    background: rgba(2, 6, 23, 0.8);
    border-bottom: 1px solid rgba(30, 41, 59, 0.5);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.8);
}

[data-theme="dark"] .navbar.scrolled {
    background: rgba(15, 23, 42, 0.95);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.9);
}

[data-theme="dark"] .service-card {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(30, 41, 59, 0.4);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.6);
}

[data-theme="dark"] .service-card:hover {
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
    border-color: rgba(96, 165, 250, 0.5);
}

[data-theme="dark"] .portfolio-item {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(30, 41, 59, 0.4);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
}

[data-theme="dark"] .portfolio-item:hover {
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.8);
}

[data-theme="dark"] .cta-btn.primary {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
}

[data-theme="dark"] .cta-btn.primary:hover {
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.8);
}

[data-theme="dark"] .cta-btn.secondary {
    background: rgba(15, 23, 42, 0.6);
    border: 1px solid rgba(30, 41, 59, 0.6);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .hero-badge {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(30, 41, 59, 0.8);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .contact-info {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(30, 41, 59, 0.4);
}

[data-theme="dark"] .timeline-content {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(30, 41, 59, 0.4);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.6);
}

[data-theme="dark"] .timeline-content:hover {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.8);
}

[data-theme="dark"] .contact-form .form-control {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(30, 41, 59, 0.6);
    color: var(--text-color);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .contact-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.2),
                0 8px 25px rgba(0, 0, 0, 0.6);
}

[data-theme="dark"] .contact-form .form-control::placeholder {
    color: var(--text-muted);
    opacity: 0.6;
}

[data-theme="dark"] footer {
    background-color: #0f172a;
    border-top: 1px solid rgba(30, 41, 59, 0.4);
}

/* Section Styles */
section {
    padding: 100px 0;
}

.section-title {
    text-align: center;
    margin-bottom: 70px;
    position: relative;
    font-size: 2.5rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: inline-block;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.section-title::before {
    content: '';
    position: absolute;
    width: 30px;
    height: 4px;
    background-color: var(--accent-color);
    bottom: -15px;
    left: 50%;
    transform: translateX(-100%);
    border-radius: 2px;
}

.section-title::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 4px;
    background-color: var(--primary-color);
    bottom: -15px;
    left: 50%;
    transform: translateX(0%);
    border-radius: 2px;
}

/* About Section */
.about-img {
    border-radius: 10px;
    box-shadow: 0 10px 30px var(--shadow-color);
    transition: transform 0.5s;
}

.about-img:hover {
    transform: scale(1.03);
}

/* Services Section */
.service-card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    padding: 2.5rem 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px var(--shadow-color);
    transition: all var(--transition-speed) var(--transition-smooth);
    border: 1px solid var(--border-color);
    height: 100%;
    position: relative;
    overflow: hidden;
}

/* Video editing interface elements */
.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right,
        var(--primary-color) 0%,
        var(--accent-color) 50%,
        var(--primary-color) 100%
    );
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.service-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 30px;
    height: 30px;
    background-image:
        linear-gradient(45deg, transparent 50%, var(--accent-color) 50%),
        linear-gradient(-45deg, transparent 50%, var(--primary-color) 50%);
    background-size: 10px 10px;
    background-position: 0 0, 10px 0;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px var(--shadow-lg);
    border-color: var(--accent-color);
}

.service-card:hover::before {
    transform: translateY(0);
}

.service-card:hover::after {
    opacity: 0.7;
}

.service-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius);
    background: linear-gradient(135deg,
        rgba(var(--primary-color-rgb), 0.1) 0%,
        rgba(var(--accent-color-rgb), 0.1) 100%
    );
    box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.15);
    transition: all var(--transition-speed) var(--transition-smooth);
}

.service-card:hover .service-icon {
    transform: scale(1.05) rotate(3deg);
    box-shadow: 0 12px 35px rgba(var(--primary-color-rgb), 0.25);
    background: linear-gradient(135deg,
        rgba(var(--primary-color-rgb), 0.2) 0%,
        rgba(var(--accent-color-rgb), 0.2) 100%
    );
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.2rem;
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.4);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    opacity: 0;
    visibility: hidden;
    z-index: 999;
    text-decoration: none;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(var(--primary-color-rgb), 0.6);
    color: white;
}

/* Portfolio Section */
#portfolio {
    position: relative;
}

/* Video timeline effect */
#portfolio::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 10px;
    background: linear-gradient(90deg,
        var(--accent-color) 0%,
        var(--primary-color) 25%,
        var(--accent-color) 50%,
        var(--primary-color) 75%,
        var(--accent-color) 100%
    );
    opacity: 0.7;
}

#portfolio::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 10px;
    background: linear-gradient(90deg,
        var(--primary-color) 0%,
        var(--accent-color) 25%,
        var(--primary-color) 50%,
        var(--accent-color) 75%,
        var(--primary-color) 100%
    );
    opacity: 0.7;
}

.portfolio-filters {
    display: flex;
    justify-content: center;
    margin-bottom: 40px;
}

.filter-btn {
    background: none;
    border: none;
    padding: 8px 20px;
    margin: 0 5px;
    cursor: pointer;
    color: var(--text-color);
    font-weight: 500;
    transition: color 0.3s;
    position: relative;
}

.filter-btn::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--primary-color);
    transition: width 0.3s;
}

.filter-btn:hover::after,
.filter-btn.active::after {
    width: 100%;
}

.filter-btn.active {
    color: var(--primary-color);
}

.portfolio-item {
    margin-bottom: 2.5rem;
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 15px 40px var(--shadow-color);
    border: 1px solid var(--border-color);
    transition: all var(--transition-speed) var(--transition-smooth);
    transform: translateY(0);
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

/* Video frame effect */
.portfolio-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(to right, var(--primary-color) 4px, transparent 4px) 0 0,
        linear-gradient(to right, var(--primary-color) 4px, transparent 4px) 0 100%,
        linear-gradient(to left, var(--primary-color) 4px, transparent 4px) 100% 0,
        linear-gradient(to left, var(--primary-color) 4px, transparent 4px) 100% 100%,
        linear-gradient(to bottom, var(--primary-color) 4px, transparent 4px) 0 0,
        linear-gradient(to bottom, var(--primary-color) 4px, transparent 4px) 100% 0,
        linear-gradient(to top, var(--primary-color) 4px, transparent 4px) 0 100%,
        linear-gradient(to top, var(--primary-color) 4px, transparent 4px) 100% 100%;
    background-repeat: no-repeat;
    background-size: 25px 25px;
    z-index: 2;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.portfolio-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom,
        rgba(var(--primary-color-rgb), 0.1) 0%,
        rgba(var(--accent-color-rgb), 0.1) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 1;
    pointer-events: none;
}

.portfolio-item:hover {
    border-color: var(--accent-color);
    box-shadow: 0 25px 60px var(--shadow-lg);
    transform: translateY(-12px) scale(1.02);
}

.portfolio-item:hover::before {
    opacity: 0.9;
}

.portfolio-item:hover::after {
    opacity: 1;
}

.portfolio-img {
    transition: transform 0.5s;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom,
        rgba(0, 0, 0, 0.7) 0%,
        rgba(var(--primary-color-rgb), 0.7) 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    padding: 30px;
    text-align: center;
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    z-index: 3;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-item:hover .portfolio-img {
    transform: scale(1.1);
}

.portfolio-title {
    color: white;
    margin-bottom: 10px;
    transform: translateY(30px);
    transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: 0.05em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.portfolio-category {
    color: var(--accent-color);
    transform: translateY(30px);
    transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    margin-bottom: 20px;
    font-weight: 500;
    letter-spacing: 0.03em;
    text-transform: uppercase;
    font-size: 0.9rem;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.portfolio-overlay .btn {
    transform: translateY(30px);
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 50px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.9rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.portfolio-overlay .btn:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

/* Mobile portfolio styles */
@media (max-width: 767px) {
    .portfolio-overlay h4 {
        font-size: 1.1rem;
    }

    .portfolio-overlay p {
        font-size: 0.9rem;
        margin-bottom: 10px;
    }

    .portfolio-overlay .btn {
        padding: 5px 10px;
        font-size: 0.8rem;
    }
}

.portfolio-item:hover .portfolio-title,
.portfolio-item:hover .portfolio-category,
.portfolio-item:hover .btn {
    transform: translateY(0);
}

/* Modern Loading Animation */
.loading {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.loading.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Smooth Page Transitions */
.page-transition {
    opacity: 0;
    animation: fadeInUp 0.8s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Focus States for Accessibility */
.cta-btn:focus,
.filter-btn:focus,
.nav-link:focus,
.theme-toggle:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Modern Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-color);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
}

/* Selection Color */
::selection {
    background: rgba(var(--primary-color-rgb), 0.2);
    color: var(--text-color);
}

::-moz-selection {
    background: rgba(var(--primary-color-rgb), 0.2);
    color: var(--text-color);
}

/* Improved Typography Scale */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }

/* Modern Spacing Utilities */
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }

/* Enhanced Hover Effects */
.hover-lift {
    transition: transform var(--transition-speed) var(--transition-smooth);
}

.hover-lift:hover {
    transform: translateY(-4px);
}

.hover-glow {
    transition: box-shadow var(--transition-speed) var(--transition-smooth);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(var(--primary-color-rgb), 0.3);
}
