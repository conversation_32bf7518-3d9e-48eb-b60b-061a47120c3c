:root {
    /* Light Theme Variables */
    --primary-color: #0f3b6f; /* Much darker, deep cinematic blue */
    --primary-color-rgb: 15, 59, 111; /* Much darker RGB values for primary color */
    --secondary-color: #334455; /* Much darker secondary color */
    --secondary-color-rgb: 51, 68, 85; /* Darker RGB values for secondary color */
    --accent-color: #039b42; /* Much darker, deeper red */
    --accent-color-rgb: 192, 57, 43; /* Much darker RGB values for accent color */
    --background-color: #f9f7f4;
    --text-color: #2d3748;
    --card-bg: #ffffff;
    --border-color: #e2e8f0;
    --shadow-color: rgba(0, 0, 0, 0.08);
    --footer-bg: #f0f4f8;
    --transition-speed: 0.3s;
}

/* Dark Theme Variables */
/* [data-theme="dark"] {

    --primary-color: #7bff00;
    --secondary-color: #adb5bd;
    --accent-color: #00c9ff;
    --background-color: #121212;
    --text-color: #f8f9fa;
    --card-bg: #1e1e1e;
    --border-color: #343a40;
    --shadow-color: rgba(255, 255, 255, 0.1);
    --footer-bg: #1e1e1e;
}  */

[data-theme="dark"] {
    --primary-color: #1e40af; /* Much darker, deep blue for dark mode */
    --primary-color-rgb: 30, 64, 175; /* Much darker RGB values for primary color */
    --secondary-color: #475569; /* Much darker secondary color */
    --secondary-color-rgb: 71, 85, 105; /* Much darker RGB values for secondary color */
    --accent-color: #039b42; /* Much darker, deeper amber/orange */
    --accent-color-rgb: 194, 65, 12; /* Much darker RGB values for accent color */
    --background-color: #050508; /* Extremely dark background */
    --text-color: #e2e8f0;
    --card-bg: #0c0f16; /* Much darker card background */
    --border-color: #1a202c; /* Much darker border */
    --shadow-color: rgba(0, 0, 0, 0.7); /* More intense shadow */
    --footer-bg: #0c0f16; /* Match card background */
}


/* :root {

    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --accent-color: #00c9ff;
    --background-color: #ffffff;
    --text-color: #212529;
    --card-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --footer-bg: #f8f9fa;
    --transition-speed: 0.3s;
} */


body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    transition: background-color var(--transition-speed), color var(--transition-speed);
    line-height: 1.7;
    letter-spacing: 0.01em;
    font-weight: 400;
}

h1, h2, h3, h4, h5, h6, .navbar-brand {
    font-family: 'Orbitron', sans-serif;
    letter-spacing: 0.03em;
    font-weight: 600;
    margin-bottom: 1.2rem;
}

p {
    margin-bottom: 1.5rem;
    font-size: 1.05rem;
}

/* Navbar Styles */
.navbar {
    background-color: var(--background-color);
    box-shadow: 0 2px 10px var(--shadow-color);
    transition: background-color var(--transition-speed);
}

/* Fix for navbar toggler in dark mode */
.navbar-toggler {
    border-color: var(--border-color);
    color: var(--text-color);
    padding: 0.25rem 0.5rem;
}

.navbar-toggler-icon {
    background-image: none !important;
    display: flex;
    align-items: center;
    justify-content: center;
}

.navbar-toggler-icon::before {
    content: '\f0c9';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: var(--text-color);
}

/* Mobile navbar styles */
@media (max-width: 991px) {
    .navbar-collapse {
        background-color: var(--card-bg);
        padding: 1rem;
        border-radius: 0.5rem;
        margin-top: 0.5rem;
        box-shadow: 0 5px 15px var(--shadow-color);
    }

    .nav-item {
        margin: 0.5rem 0;
    }

    .theme-toggle {
        margin-top: 0.5rem;
        padding: 0.5rem;
        width: 100%;
        text-align: left;
    }
}

.navbar-brand {
    color: var(--primary-color) !important;
    font-weight: 700;
    transition: color var(--transition-speed);
}

.navbar-brand:hover,
.navbar-brand:focus,
.navbar-brand:active,
.navbar-brand:visited {
    color: var(--primary-color) !important;
    text-decoration: none;
}

.nav-link {
    color: var(--text-color);
    margin: 0 10px;
    position: relative;
    transition: color var(--transition-speed);
}

.nav-link:hover,
.nav-link:focus,
.nav-link:active,
.nav-link.active {
    color: var(--primary-color) !important;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--primary-color);
    transition: width var(--transition-speed);
}

.nav-link:hover::after,
.nav-link:focus::after,
.nav-link:active::after,
.nav-link.active::after {
    width: 100%;
}

/* Fix for Bootstrap's default active/focus states */
.navbar-nav .nav-link.active,
.navbar-nav .nav-link:active,
.navbar-nav .show > .nav-link {
    color: var(--primary-color) !important;
}

/* Ensure navbar brand stays primary color in all states */
.navbar .navbar-brand,
.navbar .navbar-brand:hover,
.navbar .navbar-brand:focus,
.navbar .navbar-brand:active,
.navbar .navbar-brand:visited {
    color: var(--primary-color) !important;
}

/* Theme Toggle Button */
.theme-toggle {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    cursor: pointer;
    transition: color var(--transition-speed);
}

.theme-toggle:hover {
    color: var(--primary-color);
}

/* Hero Section */
#home {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--background-color) 0%, var(--card-bg) 100%);
    position: relative;
    overflow: hidden;
    padding-top: 100px; /* Increased padding to account for fixed navbar */
}

#home::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, transparent 20%, var(--background-color) 70%);
    opacity: 0.8;
}

/* Film strip decoration */
#home::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 20px,
        rgba(var(--primary-color-rgb), 0.05) 20px,
        rgba(var(--primary-color-rgb), 0.05) 40px
    );
    pointer-events: none;
    z-index: 1;
    opacity: 0.4;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    position: relative;
    display: inline-block;
    overflow: hidden;
    animation: glitch 5s infinite;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    text-shadow: 3px 3px 0 rgba(0,0,0,0.1);
}

/* Video editing glitch effect */
@keyframes glitch {
    0%, 90%, 100% {
        transform: translate(0);
        filter: none;
    }
    92% {
        transform: translate(-2px, 1px);
        text-shadow: -2px 0 2px rgba(var(--primary-color-rgb), 0.8);
        filter: hue-rotate(5deg);
    }
    93% {
        transform: translate(2px, -1px);
        text-shadow: 2px 0 2px rgba(var(--accent-color-rgb), 0.8);
        filter: hue-rotate(-5deg);
    }
    94% {
        transform: translate(-1px, -1px);
        text-shadow: 2px 0 2px rgba(var(--primary-color-rgb), 0.8);
        filter: brightness(1.1);
    }
    95% {
        transform: translate(1px, 1px);
        text-shadow: -2px 0 2px rgba(var(--accent-color-rgb), 0.8);
        filter: contrast(1.1);
    }
    96% {
        transform: translate(0);
        filter: none;
    }
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 2.5rem;
    font-weight: 300;
    letter-spacing: 0.03em;
    opacity: 0.9;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.cta-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
    border: none;
    padding: 14px 36px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.05rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 10px 20px rgba(var(--primary-color-rgb), 0.3);
    text-decoration: none;
    display: inline-block;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.cta-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
    z-index: -1;
    transition: opacity 0.4s ease;
    opacity: 0;
}

.cta-btn:hover {
    transform: translateY(-5px) scale(1.03);
    box-shadow: 0 15px 30px rgba(var(--primary-color-rgb), 0.4);
    color: white;
}

.cta-btn:hover::before {
    opacity: 1;
}

/* Fix for dark theme CTA button */
[data-theme="dark"] .cta-btn {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .cta-btn:hover {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.7);
}

/* Section Styles */
section {
    padding: 100px 0;
}

.section-title {
    text-align: center;
    margin-bottom: 70px;
    position: relative;
    font-size: 2.5rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: inline-block;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.section-title::before {
    content: '';
    position: absolute;
    width: 30px;
    height: 4px;
    background-color: var(--accent-color);
    bottom: -15px;
    left: 50%;
    transform: translateX(-100%);
    border-radius: 2px;
}

.section-title::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 4px;
    background-color: var(--primary-color);
    bottom: -15px;
    left: 50%;
    transform: translateX(0%);
    border-radius: 2px;
}

/* About Section */
.about-img {
    border-radius: 10px;
    box-shadow: 0 10px 30px var(--shadow-color);
    transition: transform 0.5s;
}

.about-img:hover {
    transform: scale(1.03);
}

/* Services Section */
.service-card {
    background-color: var(--card-bg);
    border-radius: 16px;
    padding: 40px 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px var(--shadow-color);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid var(--border-color);
    height: 100%;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    background: linear-gradient(135deg,
        var(--card-bg) 0%,
        rgba(var(--primary-color-rgb), 0.05) 100%);
}

/* Video editing interface elements */
.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right,
        var(--primary-color) 0%,
        var(--accent-color) 50%,
        var(--primary-color) 100%
    );
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.service-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 30px;
    height: 30px;
    background-image:
        linear-gradient(45deg, transparent 50%, var(--accent-color) 50%),
        linear-gradient(-45deg, transparent 50%, var(--primary-color) 50%);
    background-size: 10px 10px;
    background-position: 0 0, 10px 0;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px var(--shadow-color);
    border-color: var(--primary-color);
}

.service-card:hover::before {
    transform: translateY(0);
}

.service-card:hover::after {
    opacity: 0.7;
}

.service-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
    width: 70px;
    height: 70px;
    line-height: 70px;
    text-align: center;
    border-radius: 50%;
    background: linear-gradient(135deg,
        rgba(var(--primary-color-rgb), 0.1) 0%,
        rgba(var(--accent-color-rgb), 0.1) 100%
    );
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.2);
    transition: transform 0.3s, box-shadow 0.3s;
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 20px rgba(var(--primary-color-rgb), 0.3);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.2rem;
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.4);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    opacity: 0;
    visibility: hidden;
    z-index: 999;
    text-decoration: none;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(var(--primary-color-rgb), 0.6);
    color: white;
}

/* Portfolio Section */
#portfolio {
    position: relative;
}

/* Video timeline effect */
#portfolio::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 10px;
    background: linear-gradient(90deg,
        var(--accent-color) 0%,
        var(--primary-color) 25%,
        var(--accent-color) 50%,
        var(--primary-color) 75%,
        var(--accent-color) 100%
    );
    opacity: 0.7;
}

#portfolio::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 10px;
    background: linear-gradient(90deg,
        var(--primary-color) 0%,
        var(--accent-color) 25%,
        var(--primary-color) 50%,
        var(--accent-color) 75%,
        var(--primary-color) 100%
    );
    opacity: 0.7;
}

.portfolio-filters {
    display: flex;
    justify-content: center;
    margin-bottom: 40px;
}

.filter-btn {
    background: none;
    border: none;
    padding: 8px 20px;
    margin: 0 5px;
    cursor: pointer;
    color: var(--text-color);
    font-weight: 500;
    transition: color 0.3s;
    position: relative;
}

.filter-btn::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--primary-color);
    transition: width 0.3s;
}

.filter-btn:hover::after,
.filter-btn.active::after {
    width: 100%;
}

.filter-btn.active {
    color: var(--primary-color);
}

.portfolio-item {
    margin-bottom: 40px;
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 10px 30px var(--shadow-color);
    border: 2px solid var(--border-color);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform: translateY(0);
}

/* Video frame effect */
.portfolio-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(to right, var(--primary-color) 4px, transparent 4px) 0 0,
        linear-gradient(to right, var(--primary-color) 4px, transparent 4px) 0 100%,
        linear-gradient(to left, var(--primary-color) 4px, transparent 4px) 100% 0,
        linear-gradient(to left, var(--primary-color) 4px, transparent 4px) 100% 100%,
        linear-gradient(to bottom, var(--primary-color) 4px, transparent 4px) 0 0,
        linear-gradient(to bottom, var(--primary-color) 4px, transparent 4px) 100% 0,
        linear-gradient(to top, var(--primary-color) 4px, transparent 4px) 0 100%,
        linear-gradient(to top, var(--primary-color) 4px, transparent 4px) 100% 100%;
    background-repeat: no-repeat;
    background-size: 25px 25px;
    z-index: 2;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.portfolio-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom,
        rgba(var(--primary-color-rgb), 0.1) 0%,
        rgba(var(--accent-color-rgb), 0.1) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 1;
    pointer-events: none;
}

.portfolio-item:hover {
    border-color: var(--accent-color);
    box-shadow: 0 15px 40px var(--shadow-color);
    transform: translateY(-10px);
}

.portfolio-item:hover::before {
    opacity: 0.9;
}

.portfolio-item:hover::after {
    opacity: 1;
}

.portfolio-img {
    transition: transform 0.5s;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom,
        rgba(0, 0, 0, 0.7) 0%,
        rgba(var(--primary-color-rgb), 0.7) 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    padding: 30px;
    text-align: center;
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    z-index: 3;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-item:hover .portfolio-img {
    transform: scale(1.1);
}

.portfolio-title {
    color: white;
    margin-bottom: 10px;
    transform: translateY(30px);
    transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: 0.05em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.portfolio-category {
    color: var(--accent-color);
    transform: translateY(30px);
    transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    margin-bottom: 20px;
    font-weight: 500;
    letter-spacing: 0.03em;
    text-transform: uppercase;
    font-size: 0.9rem;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.portfolio-overlay .btn {
    transform: translateY(30px);
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 50px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.9rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.portfolio-overlay .btn:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

/* Mobile portfolio styles */
@media (max-width: 767px) {
    .portfolio-overlay h4 {
        font-size: 1.1rem;
    }

    .portfolio-overlay p {
        font-size: 0.9rem;
        margin-bottom: 10px;
    }

    .portfolio-overlay .btn {
        padding: 5px 10px;
        font-size: 0.8rem;
    }
}

.portfolio-item:hover .portfolio-title,
.portfolio-item:hover .portfolio-category,
.portfolio-item:hover .btn {
    transform: translateY(0);
}
