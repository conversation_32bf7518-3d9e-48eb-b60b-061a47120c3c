/* Experience Section */
#experience {
    position: relative;
}

/* Video editing timeline markers */
#experience::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 30px;
    background-image:
        repeating-linear-gradient(
            to right,
            transparent,
            transparent 50px,
            var(--accent-color) 50px,
            var(--accent-color) 51px,
            transparent 51px,
            transparent 100px
        ),
        linear-gradient(
            to bottom,
            transparent 0%,
            transparent 90%,
            var(--accent-color) 90%,
            var(--accent-color) 100%
        );
    opacity: 0.3;
    pointer-events: none;
}

.timeline {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    overflow-x: hidden; /* Prevent horizontal scrolling */
    width: 100%;
}

.timeline::after {
    content: '';
    position: absolute;
    width: 3px;
    background: linear-gradient(to bottom,
        var(--primary-color) 0%,
        var(--accent-color) 50%,
        var(--primary-color) 100%
    );
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -1.5px;
}

.timeline-item {
    padding: 10px 40px;
    position: relative;
    width: 50%;
    box-sizing: border-box;
}

.timeline-item:nth-child(odd) {
    left: 0;
}

.timeline-item:nth-child(even) {
    left: 50%;
}

.timeline-item::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: var(--background-color);
    border: 3px solid var(--primary-color);
    border-radius: 50%;
    top: 15px;
    z-index: 1;
}

.timeline-item:nth-child(odd)::after {
    right: -10px;
}

.timeline-item:nth-child(even)::after {
    left: -10px;
}

.timeline-content {
    padding: 20px;
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: 0 5px 15px var(--shadow-color);
    transition: transform 0.3s, box-shadow 0.3s, border-color 0.3s;
    border: 1px solid var(--border-color);
    position: relative;
}

/* Video clip style header */
.timeline-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 8px;
    background: linear-gradient(to right,
        var(--primary-color) 0%,
        var(--accent-color) 100%
    );
    border-radius: 10px 10px 0 0;
}

/* Video editing markers */
.timeline-content::after {
    content: '';
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    background-image:
        radial-gradient(circle, var(--accent-color) 3px, transparent 3px),
        radial-gradient(circle, var(--primary-color) 3px, transparent 3px);
    background-position: 0 0, 15px 15px;
    background-size: 15px 15px;
    background-repeat: no-repeat;
    opacity: 0.5;
}

.timeline-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px var(--shadow-color);
    border-color: var(--primary-color);
}

.timeline-date {
    color: var(--primary-color);
    font-weight: 500;
}

/* Contact Section */
.contact-form .form-control {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 15px 20px;
    margin-bottom: 20px;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: all var(--transition-speed) var(--transition-smooth);
    box-shadow: 0 4px 15px var(--shadow-color);
}

.contact-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1),
                0 8px 25px var(--shadow-lg);
    outline: none;
    transform: translateY(-2px);
}

.contact-form .form-control::placeholder {
    color: var(--text-muted);
    opacity: 0.7;
}

/* Contact Form Button */
.contact-form .cta-btn {
    width: 100%;
    margin-top: 10px;
}

.contact-info {
    background-color: var(--card-bg);
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px var(--shadow-color);
    height: 100%;
}

.contact-info-item {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.contact-icon {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
    flex-shrink: 0;
}

/* Footer */
footer {
    background-color: var(--footer-bg);
    padding: 60px 0 30px;
    border-top: 1px solid var(--border-color);
}

.footer-title {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-weight: 600;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: var(--text-color);
    text-decoration: none;
    transition: color 0.3s;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.social-icons {
    display: flex;
    margin-top: 20px;
}

.social-icon {
    width: 40px;
    height: 40px;
    background-color: var(--card-bg);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
    color: var(--text-color);
    transition: background-color 0.3s, color 0.3s, transform 0.3s;
}

.social-icon:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-5px);
}

.copyright {
    text-align: center;
    padding-top: 30px;
    margin-top: 30px;
    border-top: 1px solid var(--border-color);
}

/* Custom Section Backgrounds */
.bg-section {
    background-color: var(--card-bg);
    transition: background-color var(--transition-speed);
}

/* Responsive Styles */
@media (max-width: 991px) {
    .hero-title {
        font-size: 2.5rem;
    }

    /* Adjust vertical spacing for hero section on tablets */
    #home {
        padding-top: 110px;
    }

    .hero-content {
        padding-top: 4rem !important;
    }

    .timeline {
        width: 100%;
        max-width: 100%;
        overflow-x: hidden;
    }

    .timeline::after {
        left: 31px;
    }

    .timeline-item {
        width: 100%;
        padding-left: 70px;
        padding-right: 25px;
        box-sizing: border-box;
        max-width: 100%;
    }

    .timeline-item:nth-child(even) {
        left: 0;
    }

    .timeline-item::after {
        left: 21px;
    }

    .timeline-content {
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
    }

    .service-card {
        height: auto;
        margin-bottom: 20px;
    }

    .portfolio-filters {
        flex-wrap: wrap;
        justify-content: center;
    }

    .filter-btn {
        margin-bottom: 10px;
    }

    .hero-video-section {
        margin-top: 2rem;
        max-width: 500px;
    }

    .hero-video-title {
        font-size: 1.3rem;
    }

    .play-button {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

@media (max-width: 767px) {
    section {
        padding: 70px 0;
    }

    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        margin-bottom: 40px;
        font-size: 1.8rem;
    }

    .navbar-brand {
        font-size: 1.2rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .contact-info {
        margin-top: 30px;
    }

    .footer-links {
        column-count: 2;
    }
}
