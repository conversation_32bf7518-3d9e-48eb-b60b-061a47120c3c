/* Extra Small Devices */
@media (max-width: 575px) {
    section {
        padding: 50px 0;
    }

    #home {
        padding-top: 120px; /* Further increased padding for mobile */
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 0.9rem;
    }

    .hero-description {
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    /* Adjust vertical spacing for hero content on mobile */
    .hero-content {
        padding-top: 2rem !important;
        padding-bottom: 2rem;
    }

    /* Hide scroll indicator on mobile */
    .scroll-indicator {
        display: none;
    }

    /* Stack hero buttons vertically on mobile */
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }

    .cta-btn {
        width: 100%;
        max-width: 280px;
        padding: 0.875rem 1.5rem;
        font-size: 0.95rem;
    }

    /* Hero video section mobile styles */
    .hero-video-section {
        margin-top: 1.5rem;
        max-width: 100%;
        padding: 0 1rem;
    }

    .hero-video-title {
        font-size: 1.2rem;
        margin-bottom: 1rem;
    }

    .play-button {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    /* Adjust hero badge for mobile */
    .hero-badge {
        padding: 0.375rem 1rem;
        margin-bottom: 1.5rem;
    }

    .badge-text {
        font-size: 0.75rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .portfolio-filters {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .filter-btn {
        width: 100%;
        margin: 0;
        padding: 8px 5px;
        font-size: 0.9rem;
    }

    .timeline-content {
        padding: 15px;
    }

    .timeline-content h4 {
        font-size: 1.1rem;
    }

    .timeline-content h5 {
        font-size: 0.9rem;
    }

    .timeline-content p {
        font-size: 0.9rem;
    }

    .contact-form .form-control {
        padding: 10px;
    }

    .contact-info-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .contact-icon {
        margin-bottom: 10px;
    }

    .footer-links {
        column-count: 1;
    }

    .social-icons {
        justify-content: center;
    }
}

/* Custom padding classes for more spacing options */
.pt-6 {
    padding-top: 4rem !important;
}

.pt-7 {
    padding-top: 5rem !important;
}

.pt-md-6 {
    padding-top: 4rem !important;
}

.pt-lg-7 {
    padding-top: 6rem !important;
}

/* Global fixes for horizontal scrolling */
html, body {
    overflow-x: hidden;
    width: 100%;
    position: relative;
    max-width: 100%;
}

/* Fix for images causing overflow */
img {
    max-width: 100%;
    height: auto;
}

/* Fix for tables causing overflow */
table {
    max-width: 100%;
    display: block;
    overflow-x: auto;
}

/* Mobile view specific styles */
body.mobile-view {
    width: 100%;
    overflow-x: hidden;
}

body.mobile-view .container {
    width: 100%;
    max-width: 100%;
    padding-left: 15px;
    padding-right: 15px;
    overflow-x: hidden;
}

body.mobile-view .row {
    margin-left: -10px;
    margin-right: -10px;
    width: 100%;
}

body.mobile-view [class*="col-"] {
    padding-left: 10px;
    padding-right: 10px;
    max-width: 100%;
}

body.mobile-view .navbar {
    width: 100%;
}

body.mobile-view .navbar-collapse {
    width: 100%;
}

/* Fix for portfolio items */
@media (max-width: 767px) {
    .portfolio-item {
        max-width: 100%;
    }

    .portfolio-img {
        width: 100%;
        height: auto;
    }

    .timeline {
        max-width: 100%;
        overflow-x: hidden;
    }
}

/* Fix for iOS Safari */
@supports (-webkit-touch-callout: none) {
    body.mobile-view {
        /* iOS specific override */
        min-height: -webkit-fill-available;
    }

    body.mobile-view #home {
        min-height: -webkit-fill-available;
    }
}
